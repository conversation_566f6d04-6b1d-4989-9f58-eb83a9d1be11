# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

---
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: envoy-ai-gateway-basic
spec:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: envoy-ai-gateway-basic-openai
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: api.openai.com
        port: 443
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: envoy-ai-gateway-basic-aws
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: bedrock-runtime.us-east-1.amazonaws.com
        port: 443
---
apiVersion: gateway.networking.k8s.io/v1alpha3
kind: BackendTLSPolicy
metadata:
  name: envoy-ai-gateway-basic-openai-tls
  namespace: default
spec:
  targetRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: envoy-ai-gateway-basic-openai
  validation:
    hostname: api.openai.com
    wellKnownCACertificates: System
---
apiVersion: gateway.networking.k8s.io/v1alpha3
kind: BackendTLSPolicy
metadata:
  name: envoy-ai-gateway-basic-aws-tls
  namespace: default
spec:
  targetRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: envoy-ai-gateway-basic-aws
  validation:
    hostname: bedrock-runtime.us-east-1.amazonaws.com
    wellKnownCACertificates: System
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: envoy-ai-gateway-basic
  namespace: default
  annotations:
    gateway.envoyproxy.io/backend-ref-priority: 0:envoy-ai-gateway-basic-openai:0,1:envoy-ai-gateway-basic-aws:0,2:envoy-ai-gateway-basic-testupstream:0
  ownerReferences:
    - apiVersion: aigateway.envoyproxy.io/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: AIGatewayRoute
      name: envoy-ai-gateway-basic
      uid: ""
spec:
  parentRefs:
    - name: envoy-ai-gateway-basic
      namespace: default
  rules:
    - backendRefs:
        - group: gateway.envoyproxy.io
          kind: Backend
          name: envoy-ai-gateway-basic-openai
      filters:
        - extensionRef:
            group: gateway.envoyproxy.io
            kind: HTTPRouteFilter
            name: ai-eg-host-rewrite
          type: ExtensionRef
      matches:
        - headers:
            - name: x-ai-eg-model
              type: Exact
              value: gpt-4o-mini
      timeouts:
        request: 60s
    - backendRefs:
        - group: gateway.envoyproxy.io
          kind: Backend
          name: envoy-ai-gateway-basic-aws
      filters:
        - extensionRef:
            group: gateway.envoyproxy.io
            kind: HTTPRouteFilter
            name: ai-eg-host-rewrite
          type: ExtensionRef
      matches:
        - headers:
            - name: x-ai-eg-model
              type: Exact
              value: llama3-2-1b-instruct-v1
      timeouts:
        request: 60s
    - backendRefs:
        - kind: Service
          name: envoy-ai-gateway-basic-testupstream
          port: 80
      filters:
        - extensionRef:
            group: gateway.envoyproxy.io
            kind: HTTPRouteFilter
            name: ai-eg-host-rewrite
          type: ExtensionRef
      matches:
        - headers:
            - name: x-ai-eg-model
              type: Exact
              value: some-cool-self-hosted-model
      timeouts:
        request: 60s
    - matches:
        - path:
            value: /
      name: unreachable
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyExtensionPolicy
metadata:
  name: ai-eg-eep-envoy-ai-gateway-basic
  namespace: default
spec:
  extProc:
    - backendRefs:
        - group: gateway.envoyproxy.io
          kind: Backend
          name: envoy-ai-gateway-extproc-backend
          namespace: default
      backendSettings:
        connection:
          bufferLimit: 50Mi
      metadata:
        writableNamespaces:
          - io.envoy.ai_gateway
      processingMode:
        allowModeOverride: true
        request:
          body: Buffered
        response:
          body: Buffered
  targetRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: envoy-ai-gateway-basic
status:
  ancestors: null
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: HTTPRouteFilter
metadata:
  name: ai-eg-host-rewrite
  namespace: default
spec:
  urlRewrite:
    hostname:
      type: Backend
---
apiVersion: v1
kind: Secret
metadata:
  name: envoy-ai-gateway-basic-default
  namespace: envoy-gateway-system
stringData:
  filter-config.yaml: |
    backends:
    - auth:
        apiKey:
          key: apiKey
      modelNameOverride: ""
      name: default/envoy-ai-gateway-basic-openai/route/envoy-ai-gateway-basic/rule/0/ref/0
      schema:
        name: OpenAI
        version: v1
    - auth:
        aws:
          credentialFileLiteral: |
            [default]
            aws_access_key_id = AWS_ACCESS_KEY_ID
            aws_secret_access_key = AWS_SECRET_ACCESS_KEY
          region: us-east-1
      modelNameOverride: us.meta.llama3-2-1b-instruct-v1:0
      name: default/envoy-ai-gateway-basic-aws/route/envoy-ai-gateway-basic/rule/1/ref/0
      schema:
        name: AWSBedrock
    - modelNameOverride: ""
      name: default/envoy-ai-gateway-basic-testupstream/route/envoy-ai-gateway-basic/rule/2/ref/0
      schema:
        name: OpenAI
        version: v1
    metadataNamespace: io.envoy.ai_gateway
    modelNameHeaderKey: x-ai-eg-model
    models:
    - CreatedAt: "2025-05-23T00:00:00Z"
      Name: gpt-4o-mini
      OwnedBy: openai
    - CreatedAt: "2025-05-23T00:00:00Z"
      Name: llama3-2-1b-instruct-v1
      OwnedBy: aws
    - CreatedAt: "2025-05-23T00:00:00Z"
      Name: some-cool-self-hosted-model
      OwnedBy: Envoy AI Gateway
    schema:
      name: OpenAI
      version: v1
    uuid: envoy-ai-gateway-basic
---
apiVersion: v1
kind: Secret
metadata:
  name: envoy-ai-gateway-basic-openai-apikey
  namespace: default
stringData:
  apiKey: apiKey
type: Opaque
---
apiVersion: v1
kind: Secret
metadata:
  name: envoy-ai-gateway-basic-aws-credentials
  namespace: default
stringData:
  credentials: |
    [default]
    aws_access_key_id = AWS_ACCESS_KEY_ID
    aws_secret_access_key = AWS_SECRET_ACCESS_KEY
type: Opaque
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: envoy-ai-gateway-basic
  namespace: default
spec:
  gatewayClassName: envoy-ai-gateway-basic
  listeners:
    - name: http
      port: 8888
      protocol: HTTP
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: envoy-ai-gateway-extproc-backend
  namespace: default
spec:
  endpoints:
    - unix:
        path: /var/run/translate.sock

